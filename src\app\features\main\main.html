<div class="bg-[#0d0c14] h-screen overflow-y-auto pb-40">
  <main
    class="h-screen bg-black"
    style="
      background-image: linear-gradient(
          to bottom,
          rgba(0, 0, 0, 0) 60%,
          #0d0c14 100%
        ),
        url('assets/images/backgrounds/main-bg.png');
      background-size: cover;
      background-position: center;
    "
  >
    <app-header></app-header>
    <div class="max-w-screen-2xl mx-auto py-6 md:py-10 px-4">
      <!-- main title -->
      <section
        class="flex flex-col md:flex-row items-center justify-center gap-6 md:gap-0"
      >
        <h1
          class="text-[#FFCFF5] text-2xl lg:text-[60px] font-extrabold max-w-[700px] leading-[1.2] text-center md:text-left"
        >
          Уникальное развлечение для гостей вашего тоя
        </h1>
        <div class="w-full md:w-[500px] h-auto md:h-[550px]">
          <img
            src="assets/images/main-img.png"
            alt="main-img"
            class="w-full h-full object-contain"
          />
        </div>
      </section>

      <section class="mt-20 md:mt-36 p-4">
        <h1
          class="text-[#E6BB84] font-black uppercase text-3xl md:text-[48px] text-center"
        >
          Наши игры
        </h1>
        <div
          class="flex flex-wrap gap-6 md:gap-12 mt-6 md:mt-10 items-center justify-center"
        >
          <app-card></app-card>
          <app-card></app-card>
          <app-card></app-card>
        </div>
      </section> 
    </div>

     

    <section class="mt-10 md:mt-24 py-5 md:py-16"
    style="background: linear-gradient(to bottom, #0D0C14 0%, #201B3B 30%, #201B3B 30%, #0D0C14 100%);">

      <h1
        class="text-[#E6BB84] font-black uppercase text-3xl md:text-[48px] text-center"
      >
        КАК ЭТО РАБОТАЕТ?
      </h1>
      <div class="flex items-center justify-center">
        <div>
          <div class="mt-10 md:mt-20 flex flex-col items-center">
            <div class="flex items-center justify-center gap-4">
              <div
                class="w-[80px] h-[80px] md:w-[120px] md:h-[120px] rounded-full bg-[#22AAA8] flex items-center justify-center text-[60px] md:text-[84px] text-white font-bold"
                style="box-shadow: 0px 0px 40px 0px #22aaa866"
              >
                1
              </div>
            </div>
            <div class="w-full md:w-[500px] px-4 md:px-0">
              <div
                class="text-[#E6BB84] mt-2 text-lg md:text-[20px] font-bold text-center uppercase"
              >
                Покупаете игру
              </div>
              <div class="text-white text-sm md:text-[16px] text-center mt-2">
                Вам предоставляется игра (которая не требует установки) и
                бесплатный ключ для активации игры.
              </div>
              <div class="text-white text-sm md:text-[16px] text-center mt-2">
                Вы можете приобрести необходимые устройства для игры или
                можете использовать свои устройства.
              </div>
            </div>
          </div>

          <div class="mt-10 md:mt-16 flex flex-col items-center">
            <div class="flex items-center justify-center gap-4">
              <div
                class="w-[80px] h-[80px] md:w-[120px] md:h-[120px] rounded-full bg-[#22AAA8] flex items-center justify-center text-[60px] md:text-[84px] text-white font-bold"
                style="box-shadow: 0px 0px 40px 0px #22aaa866"
              >
                2
              </div>
            </div>
            <div class="w-full md:w-[500px] px-4 md:px-0">
              <div
                class="text-[#E6BB84] mt-2 text-lg md:text-[20px] font-bold text-center uppercase"
              >
                Активируете игру
              </div>
              <div class="text-white text-sm md:text-[16px] text-center mt-2">
                При запуске игры используете ключ для активации.
              </div>
              <div class="text-white text-sm md:text-[16px] text-center mt-2">
                В течении 10 часов можно сколько угодно играть и
                перезапускать. По истечении срока игра не будет запускаться.
              </div>
            </div>
          </div>

          <div class="mt-10 md:mt-16 flex flex-col items-center">
            <div class="flex items-center justify-center gap-4">
              <div
                class="w-[80px] h-[80px] md:w-[120px] md:h-[120px] rounded-full bg-[#22AAA8] flex items-center justify-center text-[60px] md:text-[84px] text-white font-bold"
                style="box-shadow: 0px 0px 40px 0px #22aaa866"
              >
                3
              </div>
            </div>
            <div class="w-full md:w-[500px] px-4 md:px-0">
              <div
                class="text-[#E6BB84] mt-2 text-lg md:text-[20px] font-bold text-center uppercase"
              >
                Включите игру
              </div>
              <div class="text-white text-sm md:text-[16px] text-center mt-2">
                Подключите необходимые устройства (микрофоны/геймпады) к
                ноутбуку или ПК.
              </div>
              <div class="text-white text-sm md:text-[16px] text-center mt-2">
                Поиграйте сами чтобы протестировать игру перед мероприятием.
                Затем развлекайте ваших гостей!
              </div>
            </div>
          </div>

          <div class="mt-10 md:mt-16 flex flex-col items-center">
            <div class="flex items-center justify-center gap-4">
              <div
                class="w-[80px] h-[80px] md:w-[120px] md:h-[120px] rounded-full bg-[#22AAA8] flex items-center justify-center text-[60px] md:text-[84px] text-white font-bold"
                style="box-shadow: 0px 0px 40px 0px #22aaa866"
              >
                4
              </div>
            </div>
            <div class="w-full md:w-[500px] px-4 md:px-0">
              <div
                class="text-[#E6BB84] mt-2 text-lg md:text-[20px] font-bold text-center uppercase"
              >
                Покупаете ключи
              </div>
              <div class="text-white text-sm md:text-[16px] text-center mt-2">
                Игру покупаете один раз. Ключи доступа покупайте перед
                предстоящими мероприятиями.
              </div>
              <div class="text-white text-sm md:text-[16px] text-center mt-2">
                В вашем личном кабинете отображаются все приобретенные игры и
                ключи доступа.
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="mt-20 md:mt-36">
      <h1
        class="text-[#E6BB84] font-black uppercase text-3xl md:text-[48px] text-center"
      >
        Часто задаваемые Вопросы
      </h1>
      <div class="flex justify-center mt-6 md:mt-10">
        <div class="w-full lg:w-[1000px] px-4 md:px-0">
          <div
            class="w-full mt-2"
            *ngFor="let item of faqItems; let i = index"
          >
            <div
              class="w-full p-4 md:p-5 px-4 md:px-8 cursor-pointer text-white font-bold flex justify-between items-center text-sm md:text-base"
              style="
                background: linear-gradient(
                  121.9deg,
                  #1c1c1c 0%,
                  #050505 96.81%
                );
                border-radius: 10px;
                border: 1px solid rgba(43, 43, 43, 1);
              "
              (click)="toggleFaq(i)"
            >
              {{ item.question }}
              <img
                src="assets/icons/plus.svg"
                alt="arrow-down"
                class="w-5 h-5 md:w-6 md:h-6 transition-transform duration-300"
                [style.transform]="item.isOpen ? 'rotate(45deg)' : 'rotate(0deg)'"
              />
            </div>
            <div
              class="w-full overflow-hidden transition-all duration-300"
              [style.maxHeight]="item.isOpen ? '500px' : '0'"
              [style.opacity]="item.isOpen ? '1' : '0'"
              class="mt-2"
            >
              <div
                class="p-4 md:p-5 px-4 md:px-8 text-white text-sm md:text-base"
                style="
                  background: linear-gradient(
                    121.9deg,
                    rgba(28, 28, 28, 0.7) 0%,
                    rgba(5, 5, 5, 0.7) 96.81%
                  );
                  border-radius: 10px;
                  border: 1px solid rgba(43, 43, 43, 0.7);
                  border-top: none;
                  backdrop-filter: blur(5px);
                "
              >
                {{ item.answer }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </main>
</div>
