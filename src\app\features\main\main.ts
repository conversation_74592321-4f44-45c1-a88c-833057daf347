import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-main',
  standalone: false,
  templateUrl: './main.html',
  styleUrl: './main.css'
})
export class Main {
  faqItems = [
    {
      question: 'Вопрос 1',
      answer: 'Lorem ipsum dolor sit amet consectetur adipisicing elit. Quisquam, quos.',
      isOpen: false
    },
    {
      question: 'Вопрос 2',
      answer: 'Lorem ipsum dolor sit amet consectetur adipisicing elit. Quisquam, quos.',
      isOpen: false
    },
    {
      question: 'Вопрос 3',
      answer: 'Lorem ipsum dolor sit amet consectetur adipisicing elit. Quisquam, quos.',
      isOpen: false
    },
    {
      question: 'Вопрос 4',
      answer: 'Lorem ipsum dolor sit amet consectetur adipisicing elit. Quisquam, quos.',
      isOpen: false
    }
  ];

  toggleFaq(index: number) {
    this.faqItems[index].isOpen = !this.faqItems[index].isOpen;
  }
}
