<div class="flex justify-between items-center max-w-screen-2xl mx-auto p-4 relative">
  <div class="flex items-center gap-2">
    <a href="/" class="text-xl md:text-4xl font-bold text-[#A2845E] hover:underline duration-500 transition-all">Toy for Toi</a>
  </div>

  <!-- <PERSON> (Mobile) -->
  <button 
    class="md:hidden text-[#FFCFF5] p-2"
    (click)="toggleMenu()"
    aria-label="Toggle menu"
  >
    <svg 
      xmlns="http://www.w3.org/2000/svg" 
      class="h-6 w-6" 
      fill="none" 
      viewBox="0 0 24 24" 
      stroke="currentColor"
    >
      <path 
        *ngIf="!isMenuOpen"
        stroke-linecap="round" 
        stroke-linejoin="round" 
        stroke-width="2" 
        d="M4 6h16M4 12h16M4 18h16"
      />
      <path 
        *ngIf="isMenuOpen"
        stroke-linecap="round" 
        stroke-linejoin="round" 
        stroke-width="2" 
        d="M6 18L18 6M6 6l12 12"
      />
    </svg>
  </button>

  <!-- Desktop Navigation -->
  <div class="hidden md:flex items-center text-[#FFCFF5] font-medium text-base gap-10">
    <a href="/login" class="flex items-center gap-2 hover:underline duration-500 transition-all">
      <span>Личный кабинет</span>
      <img src="assets/icons/user.svg" alt="user" class="size-4" />
    </a>
    <a href="/cart" class="flex items-center gap-2 hover:underline duration-500 transition-all">
      <span>Корзина</span>
      <img src="assets/icons/cart.svg" alt="cart" class="size-4" />
    </a>
  </div>

  <!-- Mobile Navigation Menu -->
  <div 
    class="md:hidden absolute top-full left-0 right-0 bg-[#0d0c14] border-t border-[#1c1c1c] transition-all duration-300 ease-in-out"
    [class.hidden]="!isMenuOpen"
    [class.block]="isMenuOpen"
  >
    <div class="flex flex-col p-4 space-y-4">
      <a 
        href="/login" 
        class="flex items-center gap-2 text-[#FFCFF5] hover:underline duration-500 transition-all py-2"
        (click)="toggleMenu()"
      >
        <span>Личный кабинет</span>
        <img src="assets/icons/user.svg" alt="user" class="size-4" />
      </a>
      <a 
        href="/cart" 
        class="flex items-center gap-2 text-[#FFCFF5] hover:underline duration-500 transition-all py-2"
        (click)="toggleMenu()"
      >
        <span>Корзина</span>
        <img src="assets/icons/cart.svg" alt="cart" class="size-4" />
      </a>
    </div>
  </div>
</div>
